{"version": 3, "file": "strip-trailing-slashes.js", "sourceRoot": "", "sources": ["../../src/strip-trailing-slashes.ts"], "names": [], "mappings": "AAAA,oCAAoC;AACpC,+CAA+C;AAC/C,6CAA6C;AAC7C,4CAA4C;AAC5C,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,GAAW,EAAE,EAAE;IAClD,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA;IACtB,IAAI,YAAY,GAAG,CAAC,CAAC,CAAA;IACrB,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACvC,YAAY,GAAG,CAAC,CAAA;QAChB,CAAC,EAAE,CAAA;IACL,CAAC;IACD,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAA;AAC/D,CAAC,CAAA", "sourcesContent": ["// warning: extremely hot code path.\n// This has been meticulously optimized for use\n// within npm install on large package trees.\n// Do not edit without careful benchmarking.\nexport const stripTrailingSlashes = (str: string) => {\n  let i = str.length - 1\n  let slashesStart = -1\n  while (i > -1 && str.charAt(i) === '/') {\n    slashesStart = i\n    i--\n  }\n  return slashesStart === -1 ? str : str.slice(0, slashesStart)\n}\n"]}