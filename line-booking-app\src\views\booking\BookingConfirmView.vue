<template>
  <div>
    <!-- 頁面說明 -->
    <div class="mb-6">
      <h2 class="text-xl font-bold text-gray-900 mb-2">確認預約</h2>
      <p class="text-gray-600">請確認預約資訊無誤後提交</p>
    </div>

    <!-- 預約資訊摘要 -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">預約資訊</h3>
      
      <div class="space-y-4">
        <!-- 服務人員 -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div>
            <p class="font-medium text-gray-900">{{ bookingStore.selectedStaff?.name }}</p>
            <p class="text-sm text-gray-600">{{ bookingStore.selectedStaff?.title }}</p>
          </div>
        </div>

        <!-- 服務項目 -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div>
            <p class="font-medium text-gray-900">{{ bookingStore.selectedService?.name }}</p>
            <p class="text-sm text-gray-600">{{ bookingStore.selectedService?.duration }} 分鐘</p>
          </div>
        </div>

        <!-- 預約時間 -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div>
            <p class="font-medium text-gray-900">{{ formatBookingDateTime }}</p>
            <p class="text-sm text-gray-600">請準時到達</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 票券選擇 -->
    <div v-if="bookingStore.userTickets.length > 0" class="card mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">使用票券</h3>
      
      <div class="space-y-3">
        <div
          v-for="ticket in bookingStore.userTickets"
          :key="ticket.id"
          @click="toggleTicket(ticket.id)"
          class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors duration-200"
          :class="{
            'border-line-green bg-green-50': isTicketSelected(ticket.id),
            'border-gray-200 hover:border-gray-300': !isTicketSelected(ticket.id)
          }"
        >
          <div
            class="w-5 h-5 rounded border-2 flex items-center justify-center"
            :class="{
              'border-line-green bg-line-green': isTicketSelected(ticket.id),
              'border-gray-300': !isTicketSelected(ticket.id)
            }"
          >
            <svg
              v-if="isTicketSelected(ticket.id)"
              class="w-3 h-3 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
          </div>
          
          <div class="flex-1">
            <p class="font-medium text-gray-900">{{ ticket.title }}</p>
            <p class="text-sm text-gray-600">{{ ticket.description }}</p>
            <div class="flex items-center justify-between mt-1">
              <span class="text-sm text-line-green font-medium">
                {{ formatDiscount(ticket) }}
              </span>
              <span v-if="ticket.expiry_date" class="text-xs text-gray-500">
                到期：{{ formatDate(ticket.expiry_date) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 費用明細 -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">費用明細</h3>
      
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-600">服務費用</span>
          <span class="font-medium">NT$ {{ bookingStore.totalPrice.toLocaleString() }}</span>
        </div>
        
        <div v-if="bookingStore.discountAmount > 0" class="flex justify-between text-green-600">
          <span>票券折扣</span>
          <span>-NT$ {{ bookingStore.discountAmount.toLocaleString() }}</span>
        </div>
        
        <div class="border-t border-gray-200 pt-3 flex justify-between text-lg font-bold">
          <span>總計</span>
          <span class="text-line-green">NT$ {{ bookingStore.finalPrice.toLocaleString() }}</span>
        </div>
      </div>
    </div>

    <!-- 備註 -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">備註</h3>
      <textarea
        v-model="notes"
        @input="updateNotes"
        placeholder="請輸入特殊需求或備註事項..."
        rows="3"
        class="input-field resize-none"
      ></textarea>
    </div>

    <!-- 確認按鈕 -->
    <div class="card">
      <button
        @click="confirmBooking"
        :disabled="isSubmitting"
        class="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <div v-if="isSubmitting" class="flex items-center justify-center space-x-2">
          <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>提交中...</span>
        </div>
        <span v-else>確認預約</span>
      </button>
      
      <p class="text-xs text-gray-500 text-center mt-3">
        點擊確認即表示您同意我們的服務條款和取消政策
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBookingStore } from '@/stores/booking'
import { useAuthStore } from '@/stores/counter'
import type { Ticket } from '@/types'

const router = useRouter()
const bookingStore = useBookingStore()
const authStore = useAuthStore()

const notes = ref('')
const isSubmitting = ref(false)

// 計算屬性
const formatBookingDateTime = computed(() => {
  if (!bookingStore.bookingForm.booking_date || !bookingStore.bookingForm.booking_time) {
    return ''
  }
  
  const date = new Date(bookingStore.bookingForm.booking_date)
  const dateStr = date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  
  return `${dateStr} ${bookingStore.bookingForm.booking_time}`
})

// 方法
const isTicketSelected = (ticketId: string) => {
  return bookingStore.bookingForm.selected_tickets.includes(ticketId)
}

const toggleTicket = (ticketId: string) => {
  bookingStore.toggleTicket(ticketId)
}

const formatDiscount = (ticket: Ticket) => {
  if (ticket.discount_type === 'percentage') {
    return `${ticket.discount_value}% 折扣`
  } else if (ticket.discount_type === 'fixed_amount') {
    return `NT$ ${ticket.discount_value} 折抵`
  }
  return '優惠券'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-TW')
}

const updateNotes = () => {
  bookingStore.setNotes(notes.value)
}

const confirmBooking = async () => {
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  isSubmitting.value = true
  
  try {
    const booking = await bookingStore.createBooking(authStore.user.id)
    
    // 預約成功，跳轉到成功頁面
    router.push({
      name: 'booking-success',
      query: { bookingId: booking?.id }
    })
  } catch (error) {
    console.error('預約失敗:', error)
    // 這裡可以顯示錯誤訊息
  } finally {
    isSubmitting.value = false
  }
}

// 初始化
onMounted(async () => {
  // 載入用戶票券
  if (authStore.user && bookingStore.userTickets.length === 0) {
    await bookingStore.fetchUserTickets(authStore.user.id)
  }
  
  // 設置已有的備註
  if (bookingStore.bookingForm.notes) {
    notes.value = bookingStore.bookingForm.notes
  }
})
</script>
