<template>
  <div class="space-y-6">
    <!-- 頁面標題 -->
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-2">我的票券</h2>
      <p class="text-gray-600">管理您的優惠票券</p>
    </div>

    <!-- 票券統計 -->
    <div class="grid grid-cols-2 gap-4">
      <div class="card text-center bg-green-50 border border-green-200">
        <div class="text-2xl font-bold text-green-600 mb-1">{{ availableTickets.length }}</div>
        <div class="text-sm text-gray-600">可用票券</div>
      </div>
      <div class="card text-center bg-gray-50 border border-gray-200">
        <div class="text-2xl font-bold text-gray-600 mb-1">{{ usedTickets.length }}</div>
        <div class="text-sm text-gray-600">已用票券</div>
      </div>
    </div>

    <!-- 篩選器 -->
    <div class="card">
      <div class="flex space-x-2">
        <button
          v-for="filter in ticketFilters"
          :key="filter.value"
          @click="selectedFilter = filter.value"
          class="px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
          :class="selectedFilter === filter.value 
            ? 'bg-line-green text-white' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
        >
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="card animate-pulse">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 票券列表 -->
    <div v-else-if="filteredTickets.length > 0" class="space-y-4">
      <div
        v-for="ticket in filteredTickets"
        :key="ticket.id"
        class="card transition-all duration-200"
        :class="{
          'opacity-60': ticket.is_used || isExpired(ticket.expiry_date),
          'hover:shadow-lg': !ticket.is_used && !isExpired(ticket.expiry_date)
        }"
      >
        <div class="flex items-center space-x-4">
          <!-- 票券圖示 -->
          <div
            class="w-16 h-16 rounded-lg flex items-center justify-center"
            :class="getTicketIconClass(ticket.ticket_type)"
          >
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path v-if="ticket.ticket_type === 'discount'" d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
              <path v-else-if="ticket.ticket_type === 'free_service'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              <path v-else fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"/>
            </svg>
          </div>

          <!-- 票券資訊 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-gray-900 mb-1">{{ ticket.title }}</h3>
                <p v-if="ticket.description" class="text-sm text-gray-600 mb-2">{{ ticket.description }}</p>
                
                <div class="flex items-center space-x-4 text-sm">
                  <span class="font-medium text-line-green">
                    {{ formatDiscount(ticket) }}
                  </span>
                  
                  <span v-if="ticket.expiry_date" class="text-gray-500">
                    到期：{{ formatDate(ticket.expiry_date) }}
                  </span>
                </div>
              </div>

              <!-- 狀態標籤 -->
              <div class="flex flex-col items-end space-y-2">
                <span
                  v-if="ticket.is_used"
                  class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full"
                >
                  已使用
                </span>
                <span
                  v-else-if="isExpired(ticket.expiry_date)"
                  class="px-2 py-1 bg-red-100 text-red-600 text-xs font-medium rounded-full"
                >
                  已過期
                </span>
                <span
                  v-else-if="isExpiringSoon(ticket.expiry_date)"
                  class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full"
                >
                  即將到期
                </span>
                <span
                  v-else
                  class="px-2 py-1 bg-green-100 text-green-600 text-xs font-medium rounded-full"
                >
                  可使用
                </span>
              </div>
            </div>

            <!-- 使用資訊 -->
            <div v-if="ticket.is_used && ticket.used_at" class="mt-3 pt-3 border-t border-gray-200">
              <div class="flex items-center text-xs text-gray-500">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
                <span>使用時間：{{ formatDateTime(ticket.used_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空狀態 -->
    <div v-else class="card text-center py-12">
      <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        {{ getEmptyStateTitle() }}
      </h3>
      <p class="text-gray-600 mb-6">
        {{ getEmptyStateDescription() }}
      </p>
      <button
        @click="$router.push('/booking')"
        class="btn-primary"
      >
        立即預約
      </button>
    </div>

    <!-- 票券使用說明 -->
    <div class="card bg-blue-50 border border-blue-200">
      <h4 class="font-medium text-gray-900 mb-3">票券使用說明</h4>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• 票券僅限本人使用，不可轉讓</li>
        <li>• 請在有效期限內使用，過期無效</li>
        <li>• 每次預約僅能使用一張票券</li>
        <li>• 票券使用後無法退還或重複使用</li>
        <li>• 如有疑問請聯絡客服</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBookingStore } from '@/stores/booking'
import { useAuthStore } from '@/stores/counter'
import type { Ticket, TicketType } from '@/types'

const bookingStore = useBookingStore()
const authStore = useAuthStore()

// 狀態
const isLoading = ref(false)
const selectedFilter = ref<'all' | 'available' | 'used' | 'expired'>('all')

// 篩選器
const ticketFilters = [
  { value: 'all', label: '全部' },
  { value: 'available', label: '可用' },
  { value: 'used', label: '已用' },
  { value: 'expired', label: '過期' }
]

// 計算屬性
const availableTickets = computed(() => 
  bookingStore.userTickets.filter(ticket => 
    !ticket.is_used && !isExpired(ticket.expiry_date)
  )
)

const usedTickets = computed(() => 
  bookingStore.userTickets.filter(ticket => ticket.is_used)
)

const filteredTickets = computed(() => {
  switch (selectedFilter.value) {
    case 'available':
      return availableTickets.value
    case 'used':
      return usedTickets.value
    case 'expired':
      return bookingStore.userTickets.filter(ticket => 
        !ticket.is_used && isExpired(ticket.expiry_date)
      )
    default:
      return bookingStore.userTickets
  }
})

// 方法
const isExpired = (expiryDate?: string) => {
  if (!expiryDate) return false
  return new Date(expiryDate) < new Date()
}

const isExpiringSoon = (expiryDate?: string) => {
  if (!expiryDate) return false
  const expiry = new Date(expiryDate)
  const now = new Date()
  const diffDays = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 7 && diffDays > 0
}

const getTicketIconClass = (type: TicketType) => {
  const classes = {
    discount: 'bg-purple-100 text-purple-600',
    free_service: 'bg-green-100 text-green-600',
    cashback: 'bg-orange-100 text-orange-600'
  }
  return classes[type] || 'bg-gray-100 text-gray-600'
}

const formatDiscount = (ticket: Ticket) => {
  if (ticket.discount_type === 'percentage') {
    return `${ticket.discount_value}% 折扣`
  } else if (ticket.discount_type === 'fixed_amount') {
    return `NT$ ${ticket.discount_value} 折抵`
  }
  return '優惠券'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-TW')
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-TW')
}

const getEmptyStateTitle = () => {
  switch (selectedFilter.value) {
    case 'available':
      return '暫無可用票券'
    case 'used':
      return '暫無使用記錄'
    case 'expired':
      return '暫無過期票券'
    default:
      return '暫無票券'
  }
}

const getEmptyStateDescription = () => {
  switch (selectedFilter.value) {
    case 'available':
      return '您目前沒有可用的票券，完成預約可獲得獎勵票券'
    case 'used':
      return '您還沒有使用過任何票券'
    case 'expired':
      return '您沒有過期的票券'
    default:
      return '您還沒有任何票券，完成預約可獲得獎勵票券'
  }
}

// 載入資料
const loadTickets = async () => {
  if (!authStore.user) return
  
  isLoading.value = true
  try {
    await bookingStore.fetchUserTickets(authStore.user.id)
  } catch (error) {
    console.error('載入票券失敗:', error)
  } finally {
    isLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadTickets()
})
</script>
