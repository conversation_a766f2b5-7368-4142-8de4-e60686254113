<template>
  <div>
    <!-- 頁面說明 -->
    <div class="mb-6">
      <h2 class="text-xl font-bold text-gray-900 mb-2">選擇日期時間</h2>
      <p class="text-gray-600">請選擇您希望預約的日期和時間</p>
    </div>

    <!-- 預約摘要 -->
    <div class="card mb-6 bg-blue-50 border border-blue-200">
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">服務人員</span>
          <span class="font-medium text-gray-900">{{ bookingStore.selectedStaff?.name }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">服務項目</span>
          <span class="font-medium text-gray-900">{{ bookingStore.selectedService?.name }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">服務時長</span>
          <span class="font-medium text-gray-900">{{ bookingStore.selectedService?.duration }} 分鐘</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">服務費用</span>
          <span class="font-medium text-line-green">NT$ {{ bookingStore.selectedService?.price.toLocaleString() }}</span>
        </div>
      </div>
    </div>

    <!-- 日期選擇 -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">選擇日期</h3>
      
      <!-- 月份導航 -->
      <div class="flex items-center justify-between mb-4">
        <button
          @click="previousMonth"
          class="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </button>
        
        <h4 class="text-lg font-semibold text-gray-900">
          {{ currentMonth.toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' }) }}
        </h4>
        
        <button
          @click="nextMonth"
          class="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
        </button>
      </div>

      <!-- 日曆 -->
      <div class="grid grid-cols-7 gap-1 mb-4">
        <!-- 星期標題 -->
        <div v-for="day in weekDays" :key="day" class="text-center text-sm font-medium text-gray-500 py-2">
          {{ day }}
        </div>
        
        <!-- 日期格子 -->
        <button
          v-for="date in calendarDates"
          :key="date.dateString"
          @click="selectDate(date.dateString)"
          :disabled="!date.available || date.isPast"
          class="aspect-square flex items-center justify-center text-sm rounded-lg transition-colors duration-200"
          :class="getDateClass(date)"
        >
          {{ date.day }}
        </button>
      </div>
    </div>

    <!-- 時間選擇 -->
    <div v-if="selectedDate" class="card">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        選擇時間 - {{ formatSelectedDate }}
      </h3>
      
      <!-- 載入時間段 -->
      <div v-if="isLoadingTimeSlots" class="grid grid-cols-3 gap-3">
        <div v-for="i in 6" :key="i" class="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>
      
      <!-- 時間段列表 -->
      <div v-else-if="availableTimeSlots.length > 0" class="grid grid-cols-3 gap-3">
        <button
          v-for="timeSlot in availableTimeSlots"
          :key="timeSlot.time"
          @click="selectTime(timeSlot.time)"
          :disabled="!timeSlot.available"
          class="h-12 flex items-center justify-center text-sm font-medium rounded-lg border transition-colors duration-200"
          :class="getTimeSlotClass(timeSlot)"
        >
          {{ timeSlot.time }}
        </button>
      </div>
      
      <!-- 無可用時間 -->
      <div v-else class="text-center py-8">
        <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
          </svg>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 mb-2">該日期無可用時間</h4>
        <p class="text-gray-600">請選擇其他日期</p>
      </div>
    </div>

    <!-- 選中的日期時間資訊 -->
    <div v-if="selectedDate && selectedTime" class="mt-6 card bg-green-50 border border-green-200">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-line-green rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div>
          <p class="font-medium text-gray-900">預約時間：{{ formatSelectedDate }} {{ selectedTime }}</p>
          <p class="text-sm text-gray-600">請確認時間無誤後進行下一步</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useBookingStore } from '@/stores/booking'
import type { TimeSlot } from '@/types'

const bookingStore = useBookingStore()

// 狀態
const currentMonth = ref(new Date())
const selectedDate = ref('')
const selectedTime = ref('')
const isLoadingTimeSlots = ref(false)

// 星期標題
const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 計算屬性
const formatSelectedDate = computed(() => {
  if (!selectedDate.value) return ''
  return new Date(selectedDate.value).toLocaleDateString('zh-TW', {
    month: 'long',
    day: 'numeric',
    weekday: 'short'
  })
})

const availableTimeSlots = computed(() => {
  if (!selectedDate.value) return []
  const dateInfo = bookingStore.availableDates.find(d => d.date === selectedDate.value)
  return dateInfo?.time_slots || []
})

// 生成日曆日期
const calendarDates = computed(() => {
  const year = currentMonth.value.getFullYear()
  const month = currentMonth.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  const dates = []
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    
    const dateString = date.toISOString().split('T')[0]
    const isCurrentMonth = date.getMonth() === month
    const isPast = date < today
    
    dates.push({
      day: date.getDate(),
      dateString,
      isCurrentMonth,
      isPast,
      available: isCurrentMonth && !isPast,
      isSelected: dateString === selectedDate.value
    })
  }
  
  return dates
})

// 方法
const previousMonth = () => {
  currentMonth.value = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() - 1, 1)
}

const nextMonth = () => {
  currentMonth.value = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() + 1, 1)
}

const selectDate = async (dateString: string) => {
  selectedDate.value = dateString
  selectedTime.value = ''
  
  // 載入該日期的可用時間段
  if (bookingStore.selectedStaff) {
    isLoadingTimeSlots.value = true
    await bookingStore.checkAvailability(bookingStore.selectedStaff.id, dateString)
    isLoadingTimeSlots.value = false
  }
}

const selectTime = (time: string) => {
  selectedTime.value = time
  bookingStore.setDateTime(selectedDate.value, time)
}

const getDateClass = (date: any) => {
  const baseClass = 'border'
  
  if (!date.isCurrentMonth) {
    return `${baseClass} text-gray-300 border-transparent cursor-not-allowed`
  }
  
  if (date.isPast || !date.available) {
    return `${baseClass} text-gray-400 border-gray-200 cursor-not-allowed`
  }
  
  if (date.isSelected) {
    return `${baseClass} bg-line-green text-white border-line-green`
  }
  
  return `${baseClass} text-gray-900 border-gray-200 hover:border-line-green hover:bg-green-50`
}

const getTimeSlotClass = (timeSlot: TimeSlot) => {
  const baseClass = 'border'
  
  if (!timeSlot.available) {
    return `${baseClass} text-gray-400 border-gray-200 bg-gray-50 cursor-not-allowed`
  }
  
  if (timeSlot.time === selectedTime.value) {
    return `${baseClass} bg-line-green text-white border-line-green`
  }
  
  return `${baseClass} text-gray-900 border-gray-200 hover:border-line-green hover:bg-green-50`
}

// 監聽選中的日期時間變化
watch([selectedDate, selectedTime], ([date, time]) => {
  if (date && time) {
    bookingStore.setDateTime(date, time)
  }
})

// 初始化
onMounted(() => {
  // 如果已經選擇了日期時間，設置到本地狀態
  if (bookingStore.bookingForm.booking_date) {
    selectedDate.value = bookingStore.bookingForm.booking_date
  }
  if (bookingStore.bookingForm.booking_time) {
    selectedTime.value = bookingStore.bookingForm.booking_time
  }
})
</script>
