<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 頂部導航 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
        <h1 class="text-lg font-semibold text-gray-900">預約成功</h1>
        <button
          @click="router.push('/')"
          class="text-sm text-gray-600 hover:text-gray-900"
        >
          回首頁
        </button>
      </div>
    </header>

    <main class="max-w-md mx-auto px-4 py-6">
      <!-- 成功圖示 -->
      <div class="text-center mb-8">
        <div class="w-20 h-20 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">預約成功！</h2>
        <p class="text-gray-600">您的預約已成功提交，我們將盡快為您確認</p>
      </div>

      <!-- 預約資訊卡片 -->
      <div class="card mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">預約詳情</h3>
          <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full">
            待確認
          </span>
        </div>

        <div class="space-y-4">
          <!-- 預約編號 -->
          <div class="flex justify-between">
            <span class="text-gray-600">預約編號</span>
            <span class="font-medium text-gray-900">#{{ bookingId || 'B' + Date.now().toString().slice(-6) }}</span>
          </div>

          <!-- 服務人員 -->
          <div class="flex justify-between">
            <span class="text-gray-600">服務人員</span>
            <span class="font-medium text-gray-900">{{ bookingStore.selectedStaff?.name }}</span>
          </div>

          <!-- 服務項目 -->
          <div class="flex justify-between">
            <span class="text-gray-600">服務項目</span>
            <span class="font-medium text-gray-900">{{ bookingStore.selectedService?.name }}</span>
          </div>

          <!-- 預約時間 -->
          <div class="flex justify-between">
            <span class="text-gray-600">預約時間</span>
            <span class="font-medium text-gray-900">{{ formatBookingDateTime }}</span>
          </div>

          <!-- 服務時長 -->
          <div class="flex justify-between">
            <span class="text-gray-600">服務時長</span>
            <span class="font-medium text-gray-900">{{ bookingStore.selectedService?.duration }} 分鐘</span>
          </div>

          <!-- 費用 -->
          <div class="border-t border-gray-200 pt-4">
            <div class="flex justify-between text-lg font-bold">
              <span>總費用</span>
              <span class="text-line-green">NT$ {{ bookingStore.finalPrice.toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 重要提醒 -->
      <div class="card mb-6 bg-blue-50 border border-blue-200">
        <div class="flex items-start space-x-3">
          <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div>
            <h4 class="font-medium text-gray-900 mb-2">重要提醒</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• 我們將透過 LINE 訊息通知您預約確認結果</li>
              <li>• 請於預約時間前 10 分鐘到達</li>
              <li>• 如需取消或變更，請提前 24 小時聯絡我們</li>
              <li>• 遲到超過 15 分鐘可能需要重新安排時間</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 操作按鈕 -->
      <div class="space-y-4">
        <button
          @click="router.push('/member/bookings')"
          class="btn-primary w-full"
        >
          查看預約記錄
        </button>
        
        <button
          @click="router.push('/booking')"
          class="btn-secondary w-full"
        >
          再次預約
        </button>
        
        <button
          @click="router.push('/')"
          class="w-full text-gray-600 hover:text-gray-900 font-medium py-3"
        >
          回到首頁
        </button>
      </div>

      <!-- 聯絡資訊 -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-500 mb-2">如有任何問題，請聯絡我們</p>
        <div class="flex justify-center space-x-6 text-sm">
          <a href="tel:+886-2-1234-5678" class="text-line-green hover:underline">
            📞 (02) 1234-5678
          </a>
          <a href="mailto:<EMAIL>" class="text-line-green hover:underline">
            ✉️ 客服信箱
          </a>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useBookingStore } from '@/stores/booking'

const router = useRouter()
const route = useRoute()
const bookingStore = useBookingStore()

const bookingId = computed(() => route.query.bookingId as string)

const formatBookingDateTime = computed(() => {
  if (!bookingStore.bookingForm.booking_date || !bookingStore.bookingForm.booking_time) {
    return ''
  }
  
  const date = new Date(bookingStore.bookingForm.booking_date)
  const dateStr = date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  
  return `${dateStr} ${bookingStore.bookingForm.booking_time}`
})

onMounted(() => {
  // 如果沒有預約資訊，重定向到首頁
  if (!bookingStore.selectedStaff || !bookingStore.selectedService) {
    router.replace('/')
  }
})
</script>
