<template>
  <div>
    <!-- 頁面說明 -->
    <div class="mb-6">
      <h2 class="text-xl font-bold text-gray-900 mb-2">選擇服務項目</h2>
      <p class="text-gray-600">請選擇您需要的服務項目</p>
    </div>

    <!-- 選中的服務人員資訊 -->
    <div v-if="bookingStore.selectedStaff" class="card mb-6 bg-blue-50 border border-blue-200">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div>
          <p class="font-medium text-gray-900">服務人員：{{ bookingStore.selectedStaff.name }}</p>
          <p v-if="bookingStore.selectedStaff.title" class="text-sm text-gray-600">{{ bookingStore.selectedStaff.title }}</p>
        </div>
      </div>
    </div>

    <!-- 載入狀態 -->
    <div v-if="bookingStore.isLoading" class="space-y-4">
      <div v-for="i in 4" :key="i" class="card animate-pulse">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
          <div class="h-6 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>

    <!-- 錯誤狀態 -->
    <div v-else-if="bookingStore.error" class="card text-center py-8">
      <div class="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
        <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">載入失敗</h3>
      <p class="text-gray-600 mb-4">{{ bookingStore.error }}</p>
      <button
        @click="bookingStore.fetchServiceItems()"
        class="btn-primary"
      >
        重新載入
      </button>
    </div>

    <!-- 服務項目列表 -->
    <div v-else class="space-y-4">
      <!-- 按分類分組 -->
      <div v-for="(services, category) in groupedServices" :key="category" class="space-y-4">
        <h3 v-if="category !== 'undefined'" class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
          {{ category }}
        </h3>
        
        <div
          v-for="service in services"
          :key="service.id"
          @click="selectService(service.id)"
          class="card cursor-pointer transition-all duration-200 hover:shadow-lg"
          :class="{
            'ring-2 ring-line-green bg-green-50': bookingStore.bookingForm.service_id === service.id,
            'hover:shadow-md': bookingStore.bookingForm.service_id !== service.id
          }"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <h4 class="font-semibold text-gray-900 mb-2">{{ service.name }}</h4>
                <div
                  v-if="bookingStore.bookingForm.service_id === service.id"
                  class="w-6 h-6 bg-line-green rounded-full flex items-center justify-center ml-3 flex-shrink-0"
                >
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              
              <p v-if="service.description" class="text-sm text-gray-600 mb-3 line-clamp-2">
                {{ service.description }}
              </p>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                    <span>{{ service.duration }} 分鐘</span>
                  </div>
                </div>
                
                <div class="text-right">
                  <div class="text-lg font-bold text-line-green">
                    NT$ {{ service.price.toLocaleString() }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空狀態 -->
      <div v-if="bookingStore.serviceItems.length === 0" class="card text-center py-8">
        <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clip-rule="evenodd"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">暫無可用服務</h3>
        <p class="text-gray-600">目前沒有可預約的服務項目</p>
      </div>
    </div>

    <!-- 選中的服務項目資訊 -->
    <div v-if="selectedServiceInfo" class="mt-6 card bg-green-50 border border-green-200">
      <div class="flex items-start space-x-3">
        <div class="w-8 h-8 bg-line-green rounded-full flex items-center justify-center flex-shrink-0">
          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="flex-1">
          <p class="font-medium text-gray-900">已選擇：{{ selectedServiceInfo.name }}</p>
          <div class="flex items-center justify-between mt-1">
            <p class="text-sm text-gray-600">{{ selectedServiceInfo.duration }} 分鐘</p>
            <p class="text-sm font-semibold text-line-green">NT$ {{ selectedServiceInfo.price.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useBookingStore } from '@/stores/booking'
import type { ServiceItem } from '@/types'

const bookingStore = useBookingStore()

// 選中的服務項目資訊
const selectedServiceInfo = computed(() => {
  return bookingStore.selectedService
})

// 按分類分組的服務項目
const groupedServices = computed(() => {
  const groups: Record<string, ServiceItem[]> = {}
  
  bookingStore.serviceItems.forEach(service => {
    const category = service.category || '其他服務'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(service)
  })
  
  return groups
})

// 選擇服務項目
const selectService = (serviceId: string) => {
  bookingStore.setService(serviceId)
}

// 載入服務項目資料
onMounted(async () => {
  if (bookingStore.serviceItems.length === 0) {
    await bookingStore.fetchServiceItems()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
