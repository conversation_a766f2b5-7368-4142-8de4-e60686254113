<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <div class="text-center">
      <!-- 404 圖示 -->
      <div class="mb-8">
        <div class="w-32 h-32 mx-auto mb-6 text-gray-300">
          <svg fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h1 class="text-6xl font-bold text-gray-400 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">頁面不存在</h2>
        <p class="text-gray-600 mb-8">抱歉，您要找的頁面不存在或已被移除</p>
      </div>

      <!-- 操作按鈕 -->
      <div class="space-y-4">
        <button
          @click="router.push('/')"
          class="btn-primary"
        >
          回到首頁
        </button>
        
        <button
          @click="router.back()"
          class="btn-secondary"
        >
          返回上頁
        </button>
      </div>

      <!-- 快速連結 -->
      <div class="mt-8">
        <p class="text-sm text-gray-500 mb-4">您可能在找：</p>
        <div class="flex justify-center space-x-6 text-sm">
          <router-link to="/booking" class="text-line-green hover:underline">
            立即預約
          </router-link>
          <router-link to="/member" class="text-line-green hover:underline">
            會員中心
          </router-link>
          <router-link to="/login" class="text-line-green hover:underline">
            登入
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
</script>
