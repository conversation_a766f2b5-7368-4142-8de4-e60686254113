<template>
  <div class="space-y-6">
    <!-- 頁面標題 -->
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-2">預約記錄</h2>
      <p class="text-gray-600">查看您的所有預約記錄</p>
    </div>

    <!-- 篩選器 -->
    <div class="card">
      <div class="flex space-x-2">
        <button
          v-for="status in statusFilters"
          :key="status.value"
          @click="selectedStatus = status.value"
          class="px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
          :class="selectedStatus === status.value 
            ? 'bg-line-green text-white' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
        >
          {{ status.label }}
        </button>
      </div>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="card animate-pulse">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
          <div class="h-6 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>

    <!-- 預約列表 -->
    <div v-else-if="filteredBookings.length > 0" class="space-y-4">
      <div
        v-for="booking in filteredBookings"
        :key="booking.id"
        class="card hover:shadow-lg transition-shadow duration-200"
      >
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-2">
              <h3 class="font-semibold text-gray-900">{{ booking.service?.name }}</h3>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(booking.status)"
              >
                {{ getStatusText(booking.status) }}
              </span>
            </div>
            
            <div class="space-y-1 text-sm text-gray-600">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
                <span>{{ booking.staff?.name }}</span>
              </div>
              
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                <span>{{ formatDateTime(booking.booking_date, booking.booking_time) }}</span>
              </div>
              
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                </svg>
                <span>{{ booking.service?.duration }} 分鐘</span>
              </div>
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-lg font-bold text-line-green mb-2">
              NT$ {{ booking.final_price.toLocaleString() }}
            </div>
            <button
              @click="viewBookingDetail(booking)"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              查看詳情
            </button>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <div v-if="booking.status === 'pending' || booking.status === 'confirmed'" class="flex space-x-3 pt-4 border-t border-gray-200">
          <button
            v-if="booking.status === 'confirmed'"
            @click="rescheduleBooking(booking)"
            class="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 text-sm"
          >
            重新安排
          </button>
          <button
            @click="cancelBooking(booking)"
            class="flex-1 py-2 px-4 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 text-sm"
          >
            取消預約
          </button>
        </div>
      </div>
    </div>

    <!-- 空狀態 -->
    <div v-else class="card text-center py-12">
      <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        {{ selectedStatus === 'all' ? '暫無預約記錄' : `暫無${getStatusText(selectedStatus)}記錄` }}
      </h3>
      <p class="text-gray-600 mb-6">
        {{ selectedStatus === 'all' ? '您還沒有任何預約記錄' : '切換其他狀態查看更多記錄' }}
      </p>
      <button
        @click="$router.push('/booking')"
        class="btn-primary"
      >
        立即預約
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBookingStore } from '@/stores/booking'
import { useAuthStore } from '@/stores/counter'
import type { Booking, BookingStatus } from '@/types'

const router = useRouter()
const bookingStore = useBookingStore()
const authStore = useAuthStore()

// 狀態
const isLoading = ref(false)
const selectedStatus = ref<BookingStatus | 'all'>('all')

// 狀態篩選器
const statusFilters = [
  { value: 'all', label: '全部' },
  { value: 'pending', label: '待確認' },
  { value: 'confirmed', label: '已確認' },
  { value: 'completed', label: '已完成' },
  { value: 'cancelled', label: '已取消' }
]

// 計算屬性
const filteredBookings = computed(() => {
  if (selectedStatus.value === 'all') {
    return bookingStore.userBookings
  }
  return bookingStore.userBookings.filter(booking => booking.status === selectedStatus.value)
})

// 方法
const getStatusClass = (status: BookingStatus) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: BookingStatus | 'all') => {
  const texts = {
    all: '全部',
    pending: '待確認',
    confirmed: '已確認',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatDateTime = (date: string, time: string) => {
  const dateObj = new Date(date)
  const dateStr = dateObj.toLocaleDateString('zh-TW', {
    month: 'short',
    day: 'numeric',
    weekday: 'short'
  })
  return `${dateStr} ${time}`
}

const viewBookingDetail = (booking: Booking) => {
  // 這裡可以實現查看預約詳情的邏輯
  console.log('查看預約詳情:', booking)
}

const rescheduleBooking = (booking: Booking) => {
  // 這裡可以實現重新安排預約的邏輯
  console.log('重新安排預約:', booking)
}

const cancelBooking = (booking: Booking) => {
  if (confirm('確定要取消這個預約嗎？')) {
    // 這裡可以實現取消預約的邏輯
    console.log('取消預約:', booking)
  }
}

// 載入資料
const loadBookings = async () => {
  if (!authStore.user) return
  
  isLoading.value = true
  try {
    await bookingStore.fetchUserBookings(authStore.user.id)
  } catch (error) {
    console.error('載入預約記錄失敗:', error)
  } finally {
    isLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadBookings()
})
</script>
